import {Box, Button, Chip, IconButton, Link, Paper, Typography, styled} from '@mui/material';

// Main filter paper container
export const FilterPaper = styled(Paper)(() => ({
  width: '100%',
  maxWidth: '23.75rem', // 380px
  backgroundColor: 'white',
  borderRadius: '0.5rem', // 8px
  border: '0.0625rem solid #E5E7EB', // 1px
  overflow: 'hidden',
  elevation: 8,
}));

// Filter header container
export const FilterHeader = styled(Box)(({theme}) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingLeft: theme.spacing(3),
  paddingRight: theme.spacing(3),
  paddingTop: theme.spacing(2.5),
  paddingBottom: theme.spacing(2.5),
  borderBottom: '0.0625rem solid #E5E7EB', // 1px
}));

// Filter title
export const FilterTitle = styled(Typography)(() => ({
  fontWeight: 600,
  fontSize: '1rem', // 16px
  color: '#111827',
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem', // 8px
}));

// Filter count
export const FilterCount = styled(Typography)(() => ({
  color: '#6B7280',
  fontSize: '1rem', // 16px
  fontWeight: 400,
}));

// Header actions container
export const HeaderActions = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem', // 16px
}));

// Clear all link
export const ClearAllLink = styled(Link)(() => ({
  textDecoration: 'none',
  color: '#2563EB',
  cursor: 'pointer',
  fontSize: '0.875rem', // 14px
  fontWeight: 500,
  border: 'none',
  background: 'none',
  '&:hover': {
    textDecoration: 'underline',
  },
}));

// Close button
export const CloseButton = styled(IconButton)(() => ({
  padding: '0.5rem', // 8px
  color: '#6B7280',
  '&:hover': {
    backgroundColor: '#F3F4F6',
    color: '#374151',
  },
}));

// Filter content area
export const FilterContent = styled(Box)(({theme}) => ({
  paddingLeft: theme.spacing(3),
  paddingRight: theme.spacing(3),
  paddingTop: theme.spacing(3),
  paddingBottom: theme.spacing(3),
}));

// Section container
export const SectionContainer = styled(Box)(({theme}) => ({
  '&:not(:last-child)': {
    marginBottom: theme.spacing(3),
  },
}));

// Section title
export const SectionTitle = styled(Typography)(({theme}) => ({
  marginBottom: theme.spacing(1.5),
  fontWeight: 600,
  fontSize: '0.875rem', // 14px
  color: '#111827',
}));

// Field container
export const FieldContainer = styled(Box)(({theme}) => ({
  '&:not(:last-child)': {
    marginBottom: theme.spacing(2),
  },
}));

// Options container
export const OptionsContainer = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'row',
  gap: '0.5rem', // 8px
  flexWrap: 'wrap',
}));

// Styled chip with selection state
export const StyledChip = styled(Chip)<{isSelected: boolean}>(({isSelected}) => ({
  cursor: 'pointer',
  borderRadius: '1rem', // 16px
  fontSize: '0.875rem', // 14px
  height: '2rem', // 32px
  border: '0.0625rem solid #E0E4E7', // 1px
  backgroundColor: isSelected ? '#2563EB' : 'transparent',
  color: isSelected ? 'white' : '#6B7280',
  '&:hover': {
    backgroundColor: isSelected ? '#1D4ED8' : '#F3F4F6',
    borderColor: isSelected ? '#1D4ED8' : '#D1D5DB',
  },
}));

// Styled button with selection state
export const StyledButton = styled(Button)<{isSelected: boolean}>(({isSelected}) => ({
  minWidth: 'auto',
  paddingLeft: '1rem', // 16px
  paddingRight: '1rem', // 16px
  paddingTop: '0.25rem', // 4px
  paddingBottom: '0.25rem', // 4px
  borderRadius: '0.375rem', // 6px
  fontSize: '0.875rem', // 14px
  textTransform: 'none',
  fontWeight: 500,
  border: '0.0625rem solid #E0E4E7', // 1px
  backgroundColor: isSelected ? '#2563EB' : 'transparent',
  color: isSelected ? 'white' : '#6B7280',
  '&:hover': {
    borderColor: '#2563EB',
    backgroundColor: isSelected ? '#1D4ED8' : '#F3F4F6',
    color: isSelected ? 'white' : '#374151',
  },
}));

// Filter footer
export const FilterFooter = styled(Box)(({theme}) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingLeft: theme.spacing(3),
  paddingRight: theme.spacing(3),
  paddingTop: theme.spacing(2.5),
  paddingBottom: theme.spacing(2.5),
  gap: theme.spacing(2),
  borderTop: '0.0625rem solid #E5E7EB', // 1px
  backgroundColor: '#FAFAFA',
}));

// Base footer button
export const FooterButton = styled(Button)(({theme}) => ({
  flex: 1,
  paddingTop: theme.spacing(1.25),
  paddingBottom: theme.spacing(1.25),
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.875rem', // 14px
}));

// Close footer button
export const CloseFooterButton = styled(FooterButton)(() => ({
  borderColor: '#D1D5DB',
  color: '#6B7280',
  backgroundColor: 'white',
  '&:hover': {
    borderColor: '#9CA3AF',
    backgroundColor: '#F9FAFB',
    color: '#374151',
  },
}));

// Apply footer button
export const ApplyFooterButton = styled(FooterButton)(() => ({
  fontWeight: 600,
  backgroundColor: '#111827',
  color: 'white',
  '&:hover': {
    backgroundColor: '#000000',
  },
}));
