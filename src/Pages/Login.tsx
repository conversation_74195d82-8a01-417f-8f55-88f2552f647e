import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {InputAdornment, Typography} from '@mui/material';
import Form from 'Components/Forms/Form';
import {FormikHelpers, FormikValues, useFormikContext} from 'formik';
import {loginValidationSchema} from 'Helpers/validations/auth-validations';
import useAuth from 'Hooks/useAuth';
import {useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {ILoginForm} from 'redux/auth/authApiSlice';
import {
  FieldLabel,
  ForgotPasswordContainer,
  ForgotPasswordLink,
  FormFieldContainer,
  HeaderContainer,
  IconDivider,
  LoginCard,
  LoginContainer,
  LogoContainer,
  StyledButton,
  StyledDistekLogo,
  StyledEmailIcon,
  StyledFormInput,
  StyledFormPasswordInput,
  StyledLockIcon,
  SubTitle,
  WelcomeTitle,
  errorIconStyles,
  errorStyles,
  inputStyles,
} from '../styles/pages/Login.styles';

const initialValues = {
  username: '',
  password: '',
};

const Login = () => {
  // Define input styles as a constant

  const {login, loginLoading} = useAuth();
  const [loginError, setLoginError] = useState<string | null>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const from = location.state?.from?.pathname ?? '/';

  const handleNavigation = () => {
    navigate(from, {replace: true});
  };
  const handleSubmit = async (values: ILoginForm, {setSubmitting}: FormikHelpers<ILoginForm>) => {
    setLoginError(null);
    const {success, message} = await login(values);
    if (!success) {
      if (message === 'Invalid Credentials') {
        setLoginError('Invalid credentials. Please try again.');
      } else {
        setLoginError(message);
      }
    } else {
      handleNavigation();
    }
    setSubmitting(false);
  };

  const SubmitButton = () => {
    const {isValid, dirty} = useFormikContext<FormikValues>();

    return (
      <StyledButton
        type="submit"
        variant="contained"
        fullWidth
        isLoading={loginLoading}
        disabled={loginLoading || !dirty || !isValid}
      >
        {loginLoading ? 'Signing in...' : 'Sign In'}
      </StyledButton>
    );
  };

  return (
    <LoginContainer data-testid="LoginPage">
      <LoginCard elevation={0}>
        <HeaderContainer>
          <LogoContainer>
            <StyledDistekLogo />
          </LogoContainer>

          <WelcomeTitle variant="h3">Sign in to your account</WelcomeTitle>

          <SubTitle variant="h6">Please provide your credentials to continue</SubTitle>
        </HeaderContainer>

        <Form
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={loginValidationSchema}
          validateOnBlur={true}
          validateOnChange={true}
        >
          <FormFieldContainer>
            <FieldLabel variant="body2">Email</FieldLabel>
            <StyledFormInput
              id="username"
              placeholder="Enter your email address"
              fullWidth
              sx={inputStyles}
              startAdornment={
                <InputAdornment position="start" sx={{margin: 0}}>
                  <StyledEmailIcon />
                  <IconDivider />
                </InputAdornment>
              }
            />
          </FormFieldContainer>

          <FormFieldContainer>
            <FieldLabel variant="body2">Password</FieldLabel>
            <StyledFormPasswordInput
              id="password"
              placeholder="Enter your password"
              fullWidth
              sx={inputStyles}
              startAdornment={
                <InputAdornment position="start" sx={{margin: 0}}>
                  <StyledLockIcon />
                  <IconDivider />
                </InputAdornment>
              }
            />
          </FormFieldContainer>

          <ForgotPasswordContainer>
            <ForgotPasswordLink href="#" variant="body2">
              Forgot password?
            </ForgotPasswordLink>
          </ForgotPasswordContainer>
          {loginError && (
            <Typography sx={errorStyles} variant="body2">
              <InputAdornment position="start">
                <ReportProblemOutlinedIcon sx={errorIconStyles} />
              </InputAdornment>
              {loginError}
            </Typography>
          )}
          <SubmitButton />
        </Form>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
