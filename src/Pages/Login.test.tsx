import {fireEvent, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as useAuth from 'Hooks/useAuth';
import {MemoryRouter} from 'react-router-dom';
import {renderWithStore} from 'Tests/utils/renderWithStore';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import Login from './Login';

describe('Login Component', () => {
  const mockLogin = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useAuth hook
    vi.spyOn(useAuth, 'default').mockReturnValue({
      login: mockLogin,
      loginLoading: false,
      isLoggedIn: false,
      authData: {
        accessToken: null,
        refreshToken: null,
        expires: null,
        isLoggedIn: false,
      },
      logout: vi.fn(),
      logoutLoading: false,
    });
  });

  const renderLogin = () => {
    return renderWithStore(
      <MemoryRouter>
        <Login />
      </MemoryRouter>,
    );
  };

  const fillFormWithValidData = async () => {
    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByLabelText(/password/i);

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!'); // Valid password with special character
  };

  it('renders the form with all required fields', () => {
    renderLogin();

    // Check for input fields by their IDs
    expect(screen.getByRole('textbox', {name: ''})).toBeInTheDocument(); // username input
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument(); // password input
    expect(screen.getByRole('button', {name: /Sign In/i})).toBeDisabled();
    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
  });

  it('shows validation errors when form fields are touched and left empty', async () => {
    renderLogin();

    // Get inputs by their IDs
    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByLabelText(/password/i);

    // Touch and leave empty
    await userEvent.click(usernameInput);
    await userEvent.tab();
    await userEvent.click(passwordInput);
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();

      const submitButton = screen.getByRole('button', {name: /Sign In/i});
      expect(submitButton).toBeDisabled();
    });
  });

  it('successfully submits form with valid data', async () => {
    mockLogin.mockResolvedValue({success: true, message: null});
    renderLogin();

    await fillFormWithValidData();

    // Wait for the submit button to be enabled
    await waitFor(() => {
      const submitButton = screen.getByRole('button', {name: /Sign In/i});
      expect(submitButton).not.toBeDisabled();
    });

    const submitButton = screen.getByRole('button', {name: /Sign In/i});
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123!',
      });
    });
  });

  it('handles API errors appropriately', async () => {
    mockLogin.mockResolvedValue({success: false, message: 'Login failed. Please try again.'});
    renderLogin();

    await fillFormWithValidData();

    // Wait for the submit button to be enabled
    await waitFor(() => {
      const submitButton = screen.getByRole('button', {name: /Sign In/i});
      expect(submitButton).not.toBeDisabled();
    });

    const submitButton = screen.getByRole('button', {name: /Sign In/i});
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123!',
      });
      expect(screen.getByText('Login failed. Please try again.')).toBeInTheDocument();
    });
  });
});
