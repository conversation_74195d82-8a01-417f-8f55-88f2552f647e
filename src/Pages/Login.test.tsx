import {fireEvent, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as notistack from 'notistack';
import {MemoryRouter} from 'react-router-dom';
import * as authApiSlice from 'redux/auth/authApiSlice';
import {renderWithStore} from 'Tests/utils/renderWithStore';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import Login from './Login';

describe('Login Component', () => {
  const mockEnqueueSnackbar = vi.fn();
  const mockLoginApi = vi.fn().mockReturnValue({unwrap: () => {}});

  beforeEach(() => {
    vi.clearAllMocks();

    // Spy on useLoginMutation
    vi.spyOn(authApiSlice, 'useLoginMutation').mockReturnValue([mockLoginApi, {isLoading: false, reset: vi.fn()}]);

    // Spy on useSnackbar
    vi.spyOn(notistack, 'useSnackbar').mockReturnValue({
      enqueueSnackbar: mockEnqueueSnackbar,
      closeSnackbar: vi.fn(),
    });
  });

  const renderLogin = () => {
    return renderWithStore(
      <MemoryRouter>
        <Login />
      </MemoryRouter>,
    );
  };

  const fillFormWithValidData = async () => {
    const usernameInput = screen.getByTestId('username-input');
    const passwordInput = screen.getByTestId('password-input');

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123');
  };

  it('renders the form with all required fields', () => {
    renderLogin();

    expect(screen.getByTestId('username-input')).toBeInTheDocument();
    expect(screen.getByTestId('password-input')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Submit/i})).toBeDisabled();
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeInTheDocument();
  });

  it('shows validation errors when form fields are touched and left empty', async () => {
    renderLogin();

    const requiredFields = [
      {label: /Email/i, errorMessage: 'Email is required'},
      {label: /Password/i, errorMessage: 'Password is required'},
    ];

    for (const field of requiredFields) {
      const input = screen.getByLabelText(field.label);
      await userEvent.click(input);
      await userEvent.tab();
    }

    await waitFor(() => {
      requiredFields.forEach(field => {
        expect(screen.getByText(field.errorMessage)).toBeInTheDocument();
      });

      const submitButton = screen.getByRole('button', {name: /Submit/i});
      expect(submitButton).toBeDisabled();
    });
  });

  it('successfully submits form with valid data', async () => {
    renderLogin();

    await fillFormWithValidData();

    const submitButton = screen.getByRole('button', {name: /Submit/i});
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockLoginApi).toHaveBeenCalledWith(
        expect.objectContaining({
          username: '<EMAIL>',
          password: 'password123',
        }),
      );
    });
    expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Login Successful', {variant: 'success'});
  });

  it('handles API errors appropriately', async () => {
    mockLoginApi.mockImplementationOnce(() => new Error('API Error'));
    renderLogin();

    await fillFormWithValidData();

    const submitButton = screen.getByRole('button', {name: /Submit/i});
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to login!', {variant: 'error'});
    });
  });
});
