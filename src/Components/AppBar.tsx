import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import MenuIcon from '@mui/icons-material/Menu';
import {Menu, MenuItem} from '@mui/material';
import MuiAppBar from '@mui/material/AppBar';
import IconButton from '@mui/material/IconButton';
import Toolbar from '@mui/material/Toolbar';
import Tooltip from '@mui/material/Tooltip';
import {styled} from '@mui/material/styles';
import {Box} from '@mui/system';
import useAuth from 'Hooks/useAuth';
import {memo, useState} from 'react';
import {Link} from 'react-router-dom';
import {StyledDistekLogo} from 'styles/pages/Login.styles';
import BackdropLoader from './BackdropLoader/BackdropLoader';
import Button from './Button';

const MyAppBar = styled(MuiAppBar)(({theme}) => ({
  zIndex: theme.zIndex.drawer + 1,
  boxShadow: 'none',
  backgroundColor: theme.palette.background.default,
  color: theme.palette.text.primary,
}));

interface IAppBarProps {
  open: boolean;
  isPermanent: boolean;
  toggleDrawer: () => void;
}

const MenuButton = ({open, toggleDrawer}: Partial<IAppBarProps>) => (
  <Tooltip title="Menu" sx={{mt: -1, mr: 1}}>
    <IconButton onClick={toggleDrawer}>
      <MenuIcon sx={{...(open && {color: 'secondary.main'})}} />
    </IconButton>
  </Tooltip>
);

const AppBar = ({open, toggleDrawer, isPermanent, userName}: Partial<IAppBarProps> & {userName?: string}) => {
  const APP_BAR_MARGIN = 270;
  let appBarMargin = open ? APP_BAR_MARGIN : 0;
  appBarMargin = isPermanent ? appBarMargin : 0;
  const {logout, logoutLoading} = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      {logoutLoading ? (
        <BackdropLoader />
      ) : (
        // <MyAppBar
        //   sx={theme => ({
        //     width: `calc(100% - ${appBarMargin}px)`,
        //     transition: theme.transitions.create('width', {
        //       easing: theme.transitions.easing.sharp,
        //       duration: theme.transitions.duration.leavingScreen,
        //     }),
        //     ...(appBarMargin && {
        //       transition: theme.transitions.create('width', {
        //         easing: theme.transitions.easing.easeOut,
        //         duration: theme.transitions.duration.enteringScreen,
        //       }),
        //     }),
        //   })}
        // >
        <MyAppBar
          elevation={2}
          sx={theme => ({
            backgroundColor: theme.palette.background.paper,
            width: '100%',
            height: 72,
            minHeight: 72,
            borderBottom: '1px solid #e0e0e0',
            px: 3,
            boxSizing: 'border-box',
            display: 'flex',
            justifyContent: 'center',
          })}
        >
          <Toolbar
            sx={{
              minHeight: 72,
              height: 72,
              px: 0,
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              boxSizing: 'border-box',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%',
                height: 72,
                minHeight: 72,
                boxSizing: 'border-box',
              }}
            >
              {/* Menu button and Logo on the left */}
              <Box
                component="li"
                sx={{
                  listStyle: 'none',
                  p: 0,
                  m: 0,
                  height: 72,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <MenuButton open={open} toggleDrawer={toggleDrawer} />
                <Box sx={{height: 48, display: 'flex', alignItems: 'center'}}>
                  <Link to="/dashboard">
                    {/* <img src={distekLogoPng} alt="logo" /> */}
                    <StyledDistekLogo sx={{height: 40, width: 'auto', display: 'block'}} />
                  </Link>
                </Box>
              </Box>
              {/* User/account button on the right */}
              <Box sx={{display: 'flex', alignItems: 'center', height: 72}}>
                <Button
                  startIcon={<AccountCircleIcon fontSize="small" />}
                  endIcon={<KeyboardArrowDownIcon fontSize="small" />}
                  id="menu-button"
                  aria-controls={menuOpen ? 'menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={menuOpen ? 'true' : undefined}
                  onClick={handleClick}
                  sx={{
                    border: '1px solid #ececec',
                    borderRadius: 50,
                    backgroundColor: '#ececec',
                    color: '#525252',
                    padding: '5px 15px',
                    textTransform: 'capitalize',
                    height: 40,
                    minHeight: 40,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      minWidth: 'fit-content',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      fontSize: '12px',
                      color: '#525252',
                      fontWeight: '600',
                      textAlign: 'left',
                    }}
                  >
                    {userName}
                  </Box>
                </Button>
                <Menu
                  id="menu"
                  MenuListProps={{
                    'aria-labelledby': 'menu-button',
                  }}
                  anchorEl={anchorEl}
                  open={menuOpen}
                  onClose={handleClose}
                >
                  <MenuItem onClick={handleLogout}>Logout</MenuItem>
                </Menu>
              </Box>
            </Box>
          </Toolbar>
        </MyAppBar>
      )}
    </>
  );
};

export default memo(AppBar);
