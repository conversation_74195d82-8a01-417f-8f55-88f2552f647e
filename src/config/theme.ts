// src/config/theme.ts
import {createTheme, ThemeOptions} from '@mui/material/styles';

const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: '#000000', // Black buttons
    },
    background: {
      default: '#f9f9f9',
      paper: '#ffffff',
    },
    text: {
      primary: '#000000',
      secondary: '#4F4F4F',
      disabled: '#C4C4C4',
    },
  },
  typography: {
    fontFamily: "'Lato', 'Inter', 'Roboto', sans-serif",
    h1: {
      fontSize: '2.25rem',
      fontWeight: 700,
    },
    h6: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
    },
    button: {
      textTransform: 'none',
      fontSize: '0.875rem',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          padding: '12px 24px',
          borderRadius: 8,
        },
        containedPrimary: {
          color: '#fff',
          backgroundColor: '#000',
          '&:hover': {
            backgroundColor: '#333',
          },
        },
      },
    },
  },
};

const theme = createTheme(themeOptions);

export default theme;
