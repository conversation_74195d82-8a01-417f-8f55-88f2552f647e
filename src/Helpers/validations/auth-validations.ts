import * as yup from 'yup';

// Email validation schema
export const emailValidation = yup
  .string()
  .required('Email is required')
  .email('Please enter a valid email address')
  .matches(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, 'Email format is invalid')
  .max(50, 'Email must be less than 50 characters');

// Password validation schema
export const passwordValidation = yup
  .string()
  .required('Password is required')
  .min(6, 'Password must be at least 6 characters')
  .max(128, 'Password must be less than 128 characters')
  .matches(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
  // .matches(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
  .matches(/^(?=.*\d)/, 'Password must contain at least one number')
  .matches(/^(?=.*[@$!%*?&])/, 'Password must contain at least one special character (@$!%*?&)');

// Login form validation schema
export const loginValidationSchema = yup.object({
  username: emailValidation.label('Email'),
  password: passwordValidation.label('Password'),
});

// Individual validation functions for real-time validation
export const validateEmail = (email: string): string | null => {
  try {
    emailValidation.validateSync(email);
    return null; // No error
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return error.message;
    }
    return 'Invalid email';
  }
};

export const validatePassword = (password: string): string | null => {
  try {
    passwordValidation.validateSync(password);
    return null; // No error
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return error.message;
    }
    return 'Invalid password';
  }
};

// Function to get password strength
export const getPasswordStrength = (
  password: string,
): {
  score: number;
  label: string;
  color: string;
} => {
  let score = 0;

  if (password.length >= 8) score++;
  if (password.length >= 12) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[@$!%*?&]/.test(password)) score++;

  if (score <= 2) {
    return {score, label: 'Weak', color: '#ef4444'};
  } else if (score <= 4) {
    return {score, label: 'Medium', color: '#f59e0b'};
  } else {
    return {score, label: 'Strong', color: '#10b981'};
  }
};

// Common validation messages
export const validationMessages = {
  required: (field: string) => `${field} is required`,
  email: {
    invalid: 'Please enter a valid email address',
    required: 'Email is required',
    maxLength: 'Email must be less than 255 characters',
  },
  password: {
    required: 'Password is required',
    minLength: 'Password must be at least 8 characters',
    maxLength: 'Password must be less than 128 characters',
    lowercase: 'Password must contain at least one lowercase letter',
    uppercase: 'Password must contain at least one uppercase letter',
    number: 'Password must contain at least one number',
    special: 'Password must contain at least one special character (@$!%*?&)',
  },
} as const;
