# TDD for Login Flow Implementation

Version 1.0, July 2025

## Table of Contents

1. [Revision History](#revision-history)
2. [Purpose](#purpose)
3. [Scope](#scope)
4. [Intended Audience](#intended-audience)
5. [Definitions, Acronyms and Abbreviations](#definitions-acronyms-and-abbreviations)
6. [High Level Requirements](#high-level-requirements)
7. [Technical Decisions](#technical-decisions)
8. [Architecture](#architecture)
9. [Data Flow](#data-flow)
10. [Testing Plan](#testing-plan)
11. [Issues and Assumptions](#issues-and-assumptions)

## Revision History

| Revision | Issue Date | Author           | Comments                                    |
| -------- | ---------- | ---------------- | ------------------------------------------- |
| 1.0      | 22/07/2025 | System Architect | Initial draft for login flow implementation |

## Purpose

This document outlines the technical design decisions for implementing the authentication flow in the Distek SaaS Control Plane API. It captures the architectural decisions, system flow, and security considerations for the login implementation.

## Scope

This document covers:

- Authentication facade implementation
- Login flow architecture
- Token management
- Integration with authentication service
- Security considerations

## Intended Audience

- Development team
- System architects
- Security team
- QA team

## Definitions, Acronyms and Abbreviations

| Term | Definition                        |
| ---- | --------------------------------- |
| TDD  | Technical Design Document         |
| API  | Application Programming Interface |
| JWT  | JSON Web Token                    |
| OTP  | One-Time Password                 |
| DTO  | Data Transfer Object              |

## High Level Requirements

1. Implement secure user authentication flow
2. Support username/password based login
3. Implement token-based authentication
4. Follow facade pattern for authentication service integration
5. Ensure secure communication between services

## Technical Decisions

### UI/UX Decisions

1. **Design System**
   - Material-UI (MUI) as the base component library
   - Custom styled components for consistent theming
   - Responsive design with mobile-first approach
   - Custom theme provider with dark/light mode support

2. **Form Design**
   - Clean, single-column layout
   - Clear visual hierarchy with typography
   - Input field styling:
     ```typescript
     // Input field styling example
     export const StyledFormInput = styled(FormInput)(({theme}) => ({
       borderRadius: '0.5rem',
       height: '2.625rem',
       fontFamily: theme.typography.fontFamily,
       '& .MuiOutlinedInput-root': {
         // Input field styling
       },
     }));
     ```

3. **User Experience**
   - Real-time form validation
   - Clear error messaging
   - Loading states for async operations
   - Smooth transitions and animations
   - Keyboard navigation support
   - Accessible form controls

4. **Responsive Breakpoints**
   ```typescript
   // Responsive design implementation
   [theme.breakpoints.up('sm')]: {  // >600px
     maxWidth: '31.25rem',
     padding: theme.spacing(4),
   },
   [theme.breakpoints.up('md')]: {  // >900px
     maxWidth: '34.375rem',
     padding: theme.spacing(5),
   }
   ```

### Integration with @sourceloop/authentication-service

The authentication system is built on top of @sourceloop/authentication-service, which provides the core authentication functionality. This service:

1. Handles core authentication operations:
   - User authentication
   - Password management
   - Token generation and validation
   - OTP handling
   - Multi-tenant support

2. Provides the database models and repositories:
   - User model and repository
   - UserCredentials model and repository
   - Authentication-related DTOs

3. Offers extensible authentication strategies:
   - Username/password authentication
   - OTP-based authentication
   - Token-based authentication
   - Support for custom authentication providers

The facade pattern implemented in our architecture wraps the @sourceloop/authentication-service functionality while adding:

- Custom business logic
- Additional security layers
- Service integration
- Error handling specific to our use case

### Architecture Overview

The authentication system follows a facade pattern that interfaces with @sourceloop/authentication-service:

```typescript
// Login Controller - Entry point for authentication
@post('/auth/login')
async login(@requestBody() req: LoginRequest): Promise<CodeResponse>

// Login Helper Service - Business logic layer
@injectable({ scope: BindingScope.TRANSIENT })
export class LoginHelperService {
  async login(req: LoginRequest): Promise<CodeResponse>
  async getToken(payload: AuthTokenRequest): Promise<TokenResponse>
}

// Authentication Proxy - Service integration layer
export interface AuthenticationProxyService {
  login(body: LoginRequest): Promise<CodeResponse>;
  getToken(body: AuthTokenRequest): Promise<TokenResponse>;
}
```

### Data Models

#### Database Schema

The authentication system uses the following key tables:

1. **Users Table**

```typescript
export class User {
  id?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  username: string;
  email?: string;
  phone?: string;
  authClientIds?: string;
  lastLogin?: Date;
  photoUrl?: string;
  gender?: Gender;
  dob: Date;
  defaultTenantId: string;
}
```

2. **User Credentials Table**

```typescript
export class UserCredentials {
  id?: string;
  userId: string;
  authProvider: string;
  authId?: string;
  authToken?: string;
  secretKey?: string;
  password?: string;
}
```

3. **User Tenant Table**
   Manages multi-tenant relationships for users.

#### DTOs

```typescript
// Login Request DTO
export class LoginRequest {
  username: string;
  password: string;
}

// Auth Token Request DTO
export class AuthTokenRequest {
  code: string;
}
```

#### Data Relationships

- Each User has one UserCredentials record
- Users can belong to multiple tenants through UserTenant table
- OTP verification data is stored temporarily in Redis cache

## Architecture

### High Level Architecture

```mermaid
graph TD
    A[Client] --> B[Login Controller]
    B --> C[Login Helper Service]
    C --> D[Authentication Proxy]
    D --> E[Authentication Service]
    C --> F[Crypto Helper Service]
    C --> G[Notification Service]
```

### Backend Architecture

The login flow is implemented using a layered architecture:

1. **Controller Layer** (`LoginController`)
   - Handles HTTP requests
   - Input validation
   - Route definitions
   - Permission checks

2. **Service Layer** (`LoginHelperService`)
   - Business logic
   - Integration with other services
   - Token management
   - Error handling

3. **Proxy Layer** (`AuthenticationProxyService`)
   - Service communication
   - Data transformation
   - External service integration

## Data Flow

### Frontend Implementation

#### Form Handling with Formik

```typescript
// Login form validation schema
const loginValidationSchema = Yup.object().shape({
  username: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required')
});

// Login component implementation
const Login = () => {
  const {login, loginLoading} = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (values: ILoginForm) => {
    const isLoggedIn = await login(values);
    if (isLoggedIn) {
      navigate('/');
    }
  };

  return (
    <Form
      initialValues={{ username: '', password: '' }}
      validationSchema={loginValidationSchema}
      onSubmit={handleSubmit}
    >
      <FormInput id="username" />
      <FormPasswordInput id="password" />
      <Button type="submit" isLoading={loginLoading}>
        Sign in
      </Button>
    </Form>
  );
};
```

#### API Integration with RTK Query

```typescript
// Auth API slice with RTK Query
export const authApi = createApi({
  reducerPath: 'authApi',
  endpoints: builder => ({
    login: builder.mutation<CodeResponse, ILoginForm>({
      query: credentials => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    getToken: builder.mutation<TokenResponse, {code: string}>({
      query: data => ({
        url: '/auth/token',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

// Custom auth hook implementation
const useAuth = () => {
  const [loginApi] = useLoginMutation();
  const [getTokenApi] = useGetTokenMutation();

  const login = async (values: ILoginForm) => {
    try {
      // Step 1: Initial login to get auth code
      const loginResponse = await loginApi(values).unwrap();

      // Step 2: Exchange auth code for tokens
      const tokenResponse = await getTokenApi({
        code: loginResponse.code,
      }).unwrap();

      // Step 3: Update auth state in Redux
      dispatch(setCredentials(tokenResponse));
      return true;
    } catch (err) {
      handleError(err);
      return false;
    }
  };

  return {login};
};
```

#### Frontend Login Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant F as Login Form (Formik)
    participant AH as Auth Hook
    participant RTK as RTK Query
    participant BE as Backend
    participant R as Redux Store

    U->>F: Enter Credentials
    F->>F: Validate Form
    F->>AH: Submit Form
    AH->>RTK: Call loginApi
    RTK->>BE: POST /auth/login
    BE-->>RTK: Return Auth Code
    RTK->>AH: Return Response
    AH->>RTK: Call getTokenApi
    RTK->>BE: POST /auth/token
    BE-->>RTK: Return JWT Token
    RTK->>AH: Return Response
    AH->>R: Update Auth State
    R-->>U: Redirect to Dashboard
```

### Service Configuration

The integration with @sourceloop/authentication-service requires the following configuration:

```typescript
// Environment variables
CLIENT_ID=your_client_id
CLIENT_SECRET=your_client_secret
AUTH_SERVICE_URL=http://auth-service-url

// Authentication Proxy Configuration
@injectable({scope: BindingScope.TRANSIENT})
export class AuthenticationProxyServiceProvider
  implements Provider<AuthenticationProxyService> {
  constructor(
    @inject('datasources.AuthenticationService')
    protected dataSource: AuthenticationServiceDataSource,
  ) {}
}
```

### Login Flow Sequence

```mermaid
sequenceDiagram
    participant C as Client
    participant LC as LoginController
    participant LH as LoginHelperService
    participant AP as AuthenticationProxy
    participant SL as SourceloopAuth
    participant DB as Database
    participant RC as RedisCache

    C->>LC: POST /auth/login
    LC->>LH: login(request)
    LH->>AP: login(withClientCredentials)
    AP->>SL: authenticate
    SL->>DB: verify credentials
    SL->>RC: store OTP
    SL-->>AP: codeResponse
    AP-->>LH: codeResponse
    LH-->>LC: codeResponse
    LC-->>C: 200 OK + code
```

### Token Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant LC as LoginController
    participant LH as LoginHelperService
    participant AP as AuthenticationProxy
    participant SL as SourceloopAuth
    participant RC as RedisCache
    participant DB as Database

    C->>LC: POST /auth/token
    LC->>LH: getToken(code)
    LH->>AP: getToken(withClientId)
    AP->>SL: verifyCodeAndGenerateToken
    SL->>RC: verify OTP code
    SL->>DB: get user permissions
    SL->>DB: update lastLogin
    SL-->>AP: tokenResponse (JWT + refresh)
    AP-->>LH: tokenResponse
    LH-->>LC: tokenResponse
    LC-->>C: 200 OK + tokens

note over SL: Token Generation includes:
note over SL: - User details
note over SL: - Permissions
note over SL: - Tenant info
```

The token generation process in @sourceloop/authentication-service includes:

1. OTP code verification from Redis cache
2. Retrieval of user permissions and roles
3. JWT token generation with user context
4. Optional refresh token generation
5. Updating user's last login timestamp

## Testing Plan

### Frontend Unit Tests

1. **React Component Tests**

   ```typescript
   describe('Login Component', () => {
     it('should render form fields correctly', () => {
       // Test form field rendering
     });
     it('should handle form validation', () => {
       // Test email/password validation
     });
     it('should show loading state during submission', () => {
       // Test loading indicators
     });
     it('should handle authentication errors', () => {
       // Test error display
     });
   });
   ```

2. **Custom Hook Tests**

   ```typescript
   describe('useAuth Hook', () => {
     it('should handle login flow', async () => {
       // Test login process
     });
     it('should manage authentication state', () => {
       // Test state updates
     });
     it('should handle error scenarios', () => {
       // Test error handling
     });
   });
   ```

3. **Redux Store Tests**
   ```typescript
   describe('Auth Slice', () => {
     it('should update auth state on login', () => {
       // Test login reducer
     });
     it('should clear auth state on logout', () => {
       // Test logout reducer
     });
     it('should handle API errors', () => {
       // Test error handling
     });
   });
   ```

### Integration Tests

1. **Frontend Flow Tests**
   - Complete login journey
   - Form validation and feedback
   - Error notifications
   - Loading states
   - Navigation after login
   - Token refresh mechanism

2. **API Integration Tests**
   - Authentication API calls
   - Error handling
   - Token management
   - Session handling

3. **End-to-End Tests**
   - User login scenarios
   - Form validation
   - Error handling
   - Success flows
   - Session management

4. **Cross-browser Testing**
   - Chrome, Firefox, Safari compatibility
   - Mobile browser support
   - Responsive design verification

### Backend Tests

1. **Controller Tests**
   - Test input validation
   - Test permission checks
   - Test response formats

2. **Service Tests**
   - Test business logic
   - Test error handling
   - Test service integration

3. **Security Tests**
   - Password hashing
   - Token validation
   - Rate limiting
   - Session management

## Issues and Assumptions

### Assumptions

1. Authentication service is available and configured
2. Client credentials are properly configured in environment
3. Secure communication channel between services

### Security Considerations

#### Password Management

1. Passwords are hashed using secure hashing algorithms before storage
2. User credentials are stored separately from user data in UserCredentials table
3. Passwords are never stored in plain text
4. Password verification is handled by dedicated service methods:

```typescript
// User Repository password handling methods
async verifyPassword(username: string, password: string): Promise<User>;
async updatePassword(username: string, password: string, newPassword: string): Promise<User>;
async changePassword(username: string, newPassword: string, oldPassword?: string): Promise<User>;
```

#### Token Management

1. JWT tokens are used for authentication
2. Tokens are stored in UserCredentials table when needed
3. Client credentials (client_id/client_secret) are required for token generation
4. Token expiration and refresh mechanisms are implemented

#### OTP Security

1. OTP data is stored temporarily in Redis cache
2. OTP has configurable expiration time
3. OTP verification is required before token generation
4. Failed OTP attempts are tracked

#### Additional Security Measures

1. Rate limiting implementation to prevent brute force attacks
2. Session management and tracking of last login
3. Multi-tenant isolation through UserTenant relationships
4. Audit logging of authentication events

### Database Operations

#### Login Flow Database Operations

1. User lookup by username in Users table
2. Password verification against UserCredentials
3. OTP generation and storage in Redis
4. Token generation and storage
5. Update lastLogin timestamp

#### Token Generation Flow

1. Verify OTP from Redis cache
2. Generate JWT token with user permissions
3. Store refresh token in UserCredentials if needed
4. Update user login activity

#### Error Handling

1. Invalid credentials handling
2. Expired OTP handling
3. Invalid token handling
4. Rate limit exceeded handling
